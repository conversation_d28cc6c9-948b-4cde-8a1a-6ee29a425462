import express, { Request, Response } from 'express';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from '../core/ChatService';
import { MessageInterface, ChatResponse } from '../models/types';

export class WebInterface implements MessageInterface {
  private app: express.Application;
  private chatService: ChatService;

  constructor(chatService: ChatService) {
    this.chatService = chatService;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());

    // Add CORS if needed
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      next();
    });

    // Serve static files from the public directory (includes both dist and root files)
    this.app.use(express.static('public'));
  }

  private setupRoutes(): void {
    // Serve the main webui at root
    this.app.get('/', (req, res) => {
      res.sendFile('index.html', { root: 'public' });
    });

    // Main chat endpoint
    this.app.post('/chat', this.handleChatRequest.bind(this));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Get conversation history
    this.app.get('/conversation/:id', this.getConversationHistory.bind(this));

    // Continue existing conversation
    this.app.post('/conversation/:id/message', this.continueConversation.bind(this));
  }

  private async handleChatRequest(req: Request, res: Response): Promise<void> {
    try {
      const { text } = req.body;
      
      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({ 
          error: 'Request body must include a non-empty "text" field' 
        });
        return;
      }

      const result = await this.chatService.processUserMessage({ text: text.trim() });
      res.json(result.response);
      
    } catch (error) {
      DBOS.logger.error(`Error in /chat handler: ${(error as Error).message}`);
      const errorMessage = (error as Error).message;
      res.status(500).json({ 
        error: "An internal server error occurred.", 
        details: errorMessage 
      });
    }
  }

  private async continueConversation(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);
      const { text } = req.body;

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({ 
          error: 'Request body must include a non-empty "text" field' 
        });
        return;
      }

      const result = await this.chatService.continueConversation(conversationId, text.trim());
      res.json(result.response);
      
    } catch (error) {
      DBOS.logger.error(`Error continuing conversation: ${(error as Error).message}`);
      const errorMessage = (error as Error).message;
      
      if (errorMessage.includes('not found')) {
        res.status(404).json({ error: errorMessage });
      } else {
        res.status(500).json({ 
          error: "An internal server error occurred.", 
          details: errorMessage 
        });
      }
    }
  }

  private async getConversationHistory(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      // This would need to be implemented in ConversationService
      // const messages = await ConversationService.getConversationMessages(conversationId);
      // res.json({ conversationId, messages });
      
      res.status(501).json({ error: 'Not implemented yet' });
      
    } catch (error) {
      DBOS.logger.error(`Error getting conversation history: ${(error as Error).message}`);
      res.status(500).json({ 
        error: "An internal server error occurred.", 
        details: (error as Error).message 
      });
    }
  }

  // MessageInterface implementation
  async sendMessage(message: string): Promise<void> {
    // For web interface, this would typically be handled by the client
    console.log(`Web interface would send: ${message}`);
  }

  async receiveMessage(): Promise<string> {
    // For web interface, messages come through HTTP requests
    throw new Error('receiveMessage not applicable for web interface');
  }

  formatResponse(response: ChatResponse): string {
    return JSON.stringify(response);
  }

  getApp(): express.Application {
    return this.app;
  }
}
